<?php

declare(strict_types=1);

namespace Tests\Unit\Gateway;

use App\Gateway\ApiManagerGateway;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class ApiManagerGatewayTest extends TestCase
{
    private ApiManagerGateway $gateway;

    protected function setUp(): void
    {
        parent::setUp();
        $this->gateway = new ApiManagerGateway();

        // Set test configuration values
        // Always set deterministic values for testing
        Config::set('api_manager.base_url', 'https://api.test.example.com');
        Config::set('api_manager.headers', [
            'Accept' => 'application/json',
            'Authorization' => 'Bearer test-token-12345',
        ]);
        Config::set('api_manager.timeout', 30);
    }

    public function test_successful_get_request_returns_correct_status_code(): void
    {
        Http::fake([
            'https://api.test.example.com/test' => Http::response(['data' => 'success'], 200)
        ]);

        $response = $this->gateway->get('/test');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('{"data":"success"}', $response->getBody()->getContents());
    }

    public function test_successful_post_request_returns_correct_status_code(): void
    {
        Http::fake([
            'https://api.test.example.com/test' => Http::response(['created' => true], 201)
        ]);

        $response = $this->gateway->post('/test', ['name' => 'test']);

        $this->assertEquals(201, $response->getStatusCode());
        $this->assertEquals('{"created":true}', $response->getBody()->getContents());
    }

    public function test_api_error_status_codes_are_preserved(): void
    {
        Http::fake([
            'https://api.test.example.com/not-found' => Http::response(['error' => 'Not found'], 404),
            'https://api.test.example.com/server-error' => Http::response(['error' => 'Server error'], 500),
            'https://api.test.example.com/unauthorized' => Http::response(['error' => 'Unauthorized'], 401),
        ]);

        $response = $this->gateway->get('/not-found', [], [], true);
        $this->assertEquals(404, $response->getStatusCode());
        $this->assertEquals('{"error":"Not found"}', $response->getBody()->getContents());

        $response = $this->gateway->get('/server-error', [], [], true);
        $this->assertEquals(500, $response->getStatusCode());
        $this->assertEquals('{"error":"Server error"}', $response->getBody()->getContents());

        $response = $this->gateway->get('/unauthorized', [], [], true);
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertEquals('{"error":"Unauthorized"}', $response->getBody()->getContents());
    }

    public function test_connection_exception_handling(): void
    {
        Http::fake(function () {
            throw new ConnectionException('Connection timeout');
        });

        Log::shouldReceive('error')->once()->with(
            'API Manager: Connection failed',
            $this->callback(function ($context) {
                return $context['method'] === 'GET'
                    && $context['endpoint'] === '/test'
                    && $context['error'] === 'Connection timeout';
            })
        );

        $response = $this->gateway->get('/test');

        $this->assertEquals(503, $response->getStatusCode());
        $this->assertStringContainsString('Service temporarily unavailable: Connection timeout',
            $response->getBody()->getContents());
    }

    public function test_general_exception_handling(): void
    {
        Http::fake(function () {
            throw new \Exception('Unexpected error occurred');
        });

        Log::shouldReceive('error')->once()->with(
            'API Manager: Unexpected error',
            $this->callback(function ($context) {
                return $context['method'] === 'GET'
                    && $context['endpoint'] === '/test'
                    && $context['error'] === 'Unexpected error occurred';
            })
        );

        $response = $this->gateway->get('/test');

        $this->assertEquals(500, $response->getStatusCode());
        $this->assertStringContainsString('Internal server error: Unexpected error occurred',
            $response->getBody()->getContents());
    }

    public function test_all_http_methods_work(): void
    {
        Http::fake([
            'https://api.test.example.com/get' => Http::response(['method' => 'GET'], 200),
            'https://api.test.example.com/post' => Http::response(['method' => 'POST'], 201),
            'https://api.test.example.com/put' => Http::response(['method' => 'PUT'], 200),
            'https://api.test.example.com/delete' => Http::response(['method' => 'DELETE'], 204),
        ]);

        $response = $this->gateway->get('/get');
        $this->assertEquals(200, $response->getStatusCode());

        $response = $this->gateway->post('/post', ['data' => 'test']);
        $this->assertEquals(201, $response->getStatusCode());

        $response = $this->gateway->put('/put', ['data' => 'test']);
        $this->assertEquals(200, $response->getStatusCode());

        $response = $this->gateway->delete('/delete');
        $this->assertEquals(204, $response->getStatusCode());
    }

    public function test_invalid_http_method_throws_exception(): void
    {
        Log::shouldReceive('error')->once()->with(
            'API Manager: Unexpected error',
            $this->callback(function ($context) {
                return $context['method'] === 'PATCH'
                    && $context['endpoint'] === '/test'
                    && $context['error'] === 'Unsupported HTTP method: PATCH';
            })
        );

        $response = $this->gateway->request('PATCH', '/test');

        $this->assertEquals(500, $response->getStatusCode());
        $this->assertStringContainsString('Internal server error: Unsupported HTTP method: PATCH',
            $response->getBody()->getContents());
    }

    public function test_get_base_url_returns_config_value(): void
    {
        $baseUrl = $this->gateway->getBaseUrl();
        $this->assertEquals('https://api.test.example.com', $baseUrl);
    }

    public function test_get_base_url_returns_empty_string_when_not_configured(): void
    {
        Config::set('api_manager.base_url', '');

        $baseUrl = $this->gateway->getBaseUrl();
        $this->assertEquals('', $baseUrl);
    }

    public function test_request_uses_correct_headers_and_timeout(): void
    {
        Http::fake();

        $this->gateway->get('/test');

        Http::assertSent(function ($request) {
            return $request->hasHeader('Accept', 'application/json')
                && $request->hasHeader('Authorization', 'Bearer test-token-12345');
        });
    }

    public function test_get_method_passes_query_parameters(): void
    {
        Http::fake([
            'https://api.test.example.com/test*' => Http::response(['success' => true], 200)
        ]);

        $this->gateway->get('/test', ['param1' => 'value1', 'param2' => 'value2']);

        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'param1=value1')
                && str_contains($request->url(), 'param2=value2');
        });
    }
}

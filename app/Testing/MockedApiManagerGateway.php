<?php

declare(strict_types=1);

namespace App\Testing;

use App\Gateway\GatewayInterface;
use App\Testing\Fixtures\AbstractMockedResponse;
use App\Testing\Fixtures\ApiManager\VertriebswegResponse;

class MockedApiManagerGateway extends MockedGateway implements GatewayInterface
{
    /**
     * @return list<class-string<AbstractMockedResponse>>
     */
    protected function getMockedResponses(): array
    {
        /** @var class-string<AbstractMockedResponse> $vertriebsweg */
        $vertriebsweg = VertriebswegResponse::class;

        return [$vertriebsweg];
    }
}

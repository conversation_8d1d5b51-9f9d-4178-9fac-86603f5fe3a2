<?php

declare(strict_types=1);

namespace App\Gateway;

class ApiResponse
{
    public function __construct(
        public readonly int $statusCode,
        public readonly ?array $data,
        public readonly ?string $error = null,
        public readonly ?string $rawBody = null
    ) {
    }

    public function isSuccessful(): bool
    {
        return $this->statusCode >= 200 && $this->statusCode < 300;
    }

    public function isClientError(): bool
    {
        return $this->statusCode >= 400 && $this->statusCode < 500;
    }

    public function isServerError(): bool
    {
        return $this->statusCode >= 500;
    }


}

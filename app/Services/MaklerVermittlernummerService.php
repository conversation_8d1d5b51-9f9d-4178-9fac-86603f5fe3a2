<?php

declare(strict_types=1);

namespace App\Services;

use App\Gateway\GatewayInterface;
use App\Models\Gesellschaft;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class MaklerVermittlernummerService
{
    private const VERMITTLERNUMMER_CHECK_URL = '/api/v1/{user_id}/broker-nrs?insurance_company_ids[0]={company_id}';

    public function __construct(
        private readonly GatewayInterface $gateway,
    )
    {
    }

    public function hasVermittlernummer(User $user, Gesellschaft $gesellschaft): bool
    {
        $url = $this->buildVermittlernummerUrl($user->external_id, $gesellschaft->external_id);
        $response = $this->gateway->get($url);

        if (!$this->isSuccessful($response)) {
            Log::warning('Could not check Vermittlernummer for Makler at Gesellschaft', [
                'makler_id' => $user->external_id,
                'gesellschaft_id' => $gesellschaft->external_id,
                'status_code' => $response->getStatusCode(),
                'reason' => $response->getReasonPhrase(),
                'error' => (string) $response->getBody(),
            ]);
            return false;
        }

        return $this->checkVermittlernummerInResponse(
            json_decode($response->getBody()->getContents(), true),
            $gesellschaft->external_id
        );
    }

    private function buildVermittlernummerUrl(int $userId, int $gesellschaftId): string
    {
        return strtr(self::VERMITTLERNUMMER_CHECK_URL, [
            '{user_id}' => (string)$userId,
            '{company_id}' => (string)$gesellschaftId,
        ]);
    }

    private function isSuccessful(ResponseInterface $response): bool
    {
        $code = $response->getStatusCode();
        return $code >= 200 && $code < 300;
    }

    private function checkVermittlernummerInResponse(?array $data, int $gesellschaftId): bool
    {
        if (!is_array($data)) {
            Log::warning('Vermittlernummer API returned non-array data', ['data' => $data]);
            return false;
        }

        /** @var array{insurance_company_id: int} $brokerData */
        $brokerData = $data['data'] ?? [];

        return collect($brokerData)
            ->pluck('insurance_company_id')
            ->contains($gesellschaftId);
    }
}
